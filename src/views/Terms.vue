<script setup>
// Terms and Conditions Page Component
</script>

<script>
export default {
  name: 'TermsAndConditions'
}
</script>

<template>
  <div class="terms-page">
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <router-link to="/" class="logo-link">
            <img src="/logo.png" alt="MeetU" class="logo-image">
          </router-link>
        </div>
        
        <div class="nav-menu">
          <router-link to="/" class="nav-link">HOME</router-link>
        </div>
      </div>
    </nav>

    <!-- Page Content -->
    <div class="page-content">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Terms of Service</h1>
          <div class="last-updated">Last updated: July 28, 2025</div>
        </div>

        <div class="content-wrapper">
 This Terms of Service ("Terms") was written in English (US). This Terms is the legitimate agreement by and between <PERSON> and Meet<PERSON> ("MeetU" or "We"). To the extent any translated version of Terms agreement conflicts against the English version, this English version prevails.
            </p>
            
            <p>
              Tips:
              There is no tolerance for objectionable content or abusive users in MeetU, which will lead to block their ID or device as punishment. You can report these users, and MeetU will review in 24 hour about porn, violence, gambling and so on.
            </p>
            
            <h2>Summary</h2>
            <p>
              You should read Terms in full because they apply every time you visit MeetU, and is the terms of service that governs the relationship between the users who interact with MeetU, as well as MeetU brands, products and services, which We call the "MeetU Services" or "Services". By using or accessing the MeetU Services, you agree to this Terms. However, just in case you ever need a reminder of the main points, here's a quick basic Terms:
            </p>
            
            <p>
              1. You will not use MeetU Services if you are under 18.
            </p>
            
            <p>
              2. We're not responsible for any materials that you post or address while you are using MeetU and We don't monitor the contents in your personal devices, but if We find or someone alerts about your inappropriate activities, We will, in our sole discretions, remove such materials.
            </p>
            
            <p>
              3. We're not responsible for any materials that you post or address while you are using MeetU and We don't monitor the contents in your personal devices, but if We find or someone alerts about your inappropriate activities, We will, in our sole discretions, remove such materials.
            </p>
            
            <p>
              4. You will not use MeetU to do anything unlawful, misleading, malicious, or discriminatory.
            </p>
            
            <h2>The full legal bit</h2>
            <p>
              The Terms apply whenever you use MeetU, whether or not you have chosen to register with us, so please read them carefully. By accessing, using, registering for or receiving services offered on MeetU (the "Services") you are accepting and agreeing to be bound by the Terms.
            </p>
            
            <p>
              IF YOU DO NOT ACCEPT AND AGREE TO THE TERMS THEN YOU MUST NOT ACCESS OR USE THE APP OR SITE.
            </p>
          <div class="content-text">
          dd l="n-x">
<!--Cwllbddnbc -->
  </div>div
</div>
-- Foot/
ter c/diva
ss="footer">
v cl<!--aFosteot-->
r<oer="fr>
<vclas="can">
  <dvs="fr-c">
      <div div class="footer-logo"aiv class="footer-logo">
            <r uoo -l=okoi="/"ls="lg-lk"outer-link>
</          <dinmgterc="/uoi.pg"a="MeU"cl="g-img">
            <rrouter-linkuter-link to="/privacy">Privacy Policy</router-link>
          d/divv>
        </<divdla="f-lks"ass="footer-bottom">
          <pvrotr-k="/">H</-lnk
  </div>er-k="/pvy">PrvcyPly</r-lktffff;
}ou-k="/er">TrConio</ru-k>
ontainer {div 1200px;
rgin: 0 a/divto;
ng: 0 2x<d; cas="foo-bttm>
}<p>&copy;2024MeetU.Allrightsreserved.
</div>
link {/div
xt-d</fo o>
</dv>
</tlt>

<ylp>
.ms-{
 n-gh:100vh;
backgo:a-gad(135deg#000f0%,#11250%#16213100%);
 backgud-atahmn:fx;
cor:#fffff;
}

.cna{
max-dth:}1200x;
margin:0auto;
/*padding: 0N20 x;/
}

-lk{
ex-ecr:n;
}

/*Nain *id;
.navbar {
  ;1,i i;:fx;
op:0;
  d:100%;
bakgrud: rgba(10, 10,b15,a0.9);r: blur(20px);
  2);dp-fil:lr(20px);
z-ix: 1000;
  pding:1rm0;
bdr-m:1pxsd rb(0, 212, 255, 0.2);
bx-hdow:04px20pxrgba0,212,255,0.1);
}

.n-cot{
max-width:1200x;
er rgin:0at;
add: 020px;
displ:flx;
juify-ct:pc-btw;
g-ems:er;0px;
}auto;
px;
.nav-l;g{
ntd:splaycenlex;
s:altge-it:cen;
}

.lg-im{
ex-ht:m60 x;
width:auto;
{anito:tanm0.3;px;
}o;
ransform 0.3s ease;
.logo-g:hv:{hover {
  transform: scale(1.05);
}

.nav-menu {
  display: flex;
  gap: 3rem;
}

.nav-link {
  text-decoration: none;
  color: #ffffff;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 1px;
  transition: color 0.3s ease;
  text-transform: uppercase;
}

.nav-link:hover {
  color: #00d4ff;
}

/* Page Content */
.page-content {
  padding-top: 120px;
  padding-bottom: 80px;
}

.page-header {
  text-align: center;
  margin-bottom: 4rem;
}

.page-title {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, #00d4ff, #ffffff, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  letter-spacing: 2px;
}

.last-updated {
  color: #888;
  font-size: 0.9rem;
}

.content-wrapper {
  max-width: 800px;
  margin: 0 auto;
}

.content-text {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(22, 33, 62, 0.8) 100%);
  border-radius: 16px;
  padding: 3rem;
  border: 1px solid rgba(0, 212, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.content-text:hover {
  border-color: rgba(0, 212, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.content-text p {
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: #cccccc;
  font-size: 1rem;
}

.content-text p:last-child {
  margin-bottom: 0;
}

.content-text strong {
  color: #00d4ff;
  font-weight: 600;
}

.content-text h2 {
  color: #00d4ff;
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.content-text h3 {
  color: #7c3aed;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.policy-link {
  color: #00d4ff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.policy-link:hover {
  color: #7c3aed;
  text-decoration: underline;
}

/* Footer */
.footer {
  background: linear-gradient(135deg, rgba(10, 10, 15, 0.95) 0%, rgba(22, 33, 62, 0.9) 100%);
  padding: 60px 0 20px;
  border-top: 1px solid rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.footer-links {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: #cccccc;
  text-decoration: none;
  font-size: 0.9rem;
  letter-spacing: 1px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #00d4ff;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #888;
}

/* Responsive Design */
@media (max-width: 768px) {
  .logo-image {
    max-height: 45px;
  }

  .page-title {
    font-size: 2.5rem;
  }
  
  .content-text {
    padding: 2rem;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .footer-links {
    gap: 1rem;
  }
}
</style>